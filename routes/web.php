<?php

use App\Http\Controllers\Portal\AuthController;
use App\Services\SlugService;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

if (! Schema::hasTable('slugs')) {
    return;
}

Route::redirect('/', '/{locale}');

// Route::name('web.')
//     ->prefix('/'.App::getLocale())
//     ->group(function() {

//     //$slugs = SlugService::get(App::getLocale());
//     //$slugs = SlugService::cache(App::getLocale(), true);

//     SlugService::cache(App::getLocale(), false)->each(function($route) {

//         $route['route'] = Str::replaceFirst(App::getLocale().'/', '', $route['route']);

//         $className = "App\\Http\\Controllers\\".Str::ucfirst(App::get('domain')['systemname'])."\\".$route['controllable_type']."Controller";

//         $middleware = [];
//         if ($route['is_auth'] == 1) {
//             $middleware[] = 'auth:user';
//         }

//         Route::get($route['route'], [(new $className)::class, $route['controllable_method'] ?? 'show'])
//             ->middleware($middleware)
//             ->name(Str::lower($route['controllable_type'].'.'.($route['controllable_method'] ?? 'show.'.$route['sluggable_id'])));

//         if ($route['controllable_type'] == 'Auth' &&
//             $route['controllable_method'] == 'index') {

//             Route::post($route['route'], [(new $className)::class, 'login'])
//                 ->middleware($middleware)
//                 ->name(Str::lower($route['controllable_type'].'.login'));
//         }
//     });

//     Route::fallback([AuthController::class, 'index']);
// });

// Route::name(App::get('domain')->index.'.')

//     ->name('portal.')
//     ->group(function() {

// Route::group(['middleware' => 'setLocale'], function () {

//     Route::redirect('/', '/{locale}');

//   //  $slugs = SlugService::get('nl');

//    // dd($slugs);

//     Route::prefix('/{locale}')->group(function () {

//       //  $slugs = SlugService::cache('nl', true);

//         Route::get('/404', [PortalPagesController::class, 'notfound'])->name('404');

//         Route::controller(AuthController::class)
//             ->name('auth.')
//             ->middleware('UserMiddleware')
//             ->group(function() {

//                 Route::get('/login', 'index')->name('index');
//                 Route::post('/login', 'login')->name('login');

//                 Route::name('password.')->group(function () {

//                     Route::get('/forgot-password', 'passwordForget')->name('forget');
//                     Route::post('/send-password', 'passwordMail')->name('mail');

//                     Route::group(['middleware' => 'tokenAuth'], function () {
//                         Route::get('/reset-password/{token?}', 'passwordReset')->name('reset');
//                         Route::post('/reset-password/{token?}', 'passwordStore')->name('reset.save');
//                     });
//                 });

//                 Route::group(['middleware' => 'tokenAuth'], function () {
//                     Route::get('/activation/{token?}','activation')->name('activation');
//                     Route::post('/activate/{token?}', 'activate')->name('activation.activate');
//                 });
//             }
//         );

//         Route::group(['middleware' => 'userAuth'], function () {

//             Route::get('/uitloggen', [AuthController::class, 'logout'])->name('auth.logout');

//             Route::get('/login/{company:slugName}', [CompaniesController::class, 'setCompany'])->name('login.companies');

//             Route::group(['middleware' => 'companyIdCheck'], function () {

//                     Route::get('/bedrijf/wijzigen/{user}', [CompaniesController::class, 'changeCompany'])->name('login.companies.change')->middleware('signed');

                    //                     Route::get('/', [DashboardController::class, 'index']);
//                     Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard.index');

//                     Route::get('/producten', [ProductsController::class, 'index'])->name('products.index');
//                     Route::get('/producten/{product}', [ProductsController::class, 'specific'])->name('products.specific');

//                     Route::get('/offertes', [QuotesController::class, 'index'])->name('quotes.index');
//                     Route::get('/offertes/specifiek', [QuotesController::class, 'specific'])->name('quotes.specific');

//                     Route::get('/orders', [OrdersController::class, 'index'])->name('orders.index');
//                     Route::get('/orders/{order}', [OrdersController::class, 'specific'])->name('orders.specific');

//                     Route::get('/winkelwagen', [ShoppingCartController::class, 'index'])->name('shoppingcart');
//                     Route::get('/afrekenen', [CheckoutController::class, 'index'])->name('checkout');

//                     Route::get('/zoeken', [SearchController::class, 'index'])->name('search');

//                     Route::get('/account', [AccountsController::class, 'index'])->name('account.index');
//                     Route::post('/account', [AccountsController::class, 'store'])->name('account.store');

//                     Route::prefix('/account')->group(function () {

//                         Route::get('/bedrijf', [AccountsController::class, 'company'])->name('account.company');

//                         Route::get('/contactpersonen', [ContactPersonsController::class, 'index'])->name('account.contactpersons');
//                         Route::get('/contactpersonen/bewerken', [ContactPersonsController::class, 'edit'])->name('account.contactpersons.edit');

//                         Route::get('/adres', [AddressesController::class, 'index'])->name('account.addressbook');
//                         Route::get('/adres/bewerken', [AddressesController::class, 'edit'])->name('account.addressbook.edit');

//                     });

//                     Route::get('/importer', [ImporterController::class, 'index'])->name('importer.index');
//                     Route::get('/importer/worksheets', [ImporterController::class, 'worksheets'])->name('importer.worksheets');
//                     Route::get('/importer/worksheets/configureren', [ImporterController::class, 'worksheetsConfigs'])->name('importer.worksheets.configs');
//                     Route::get('/importer/worksheets/importeren', [ImporterController::class, 'worksheetsImport'])->name('importer.worksheets.import');

//                     Route::get('/sjablonen', [TemplatesController::class, 'index'])->name('templates.index');
//                     Route::get('/prijsafspraken', [CompanyPricingsController::class, 'index'])->name('company_pricings.index');

//             });
//         });
//     });
// });
