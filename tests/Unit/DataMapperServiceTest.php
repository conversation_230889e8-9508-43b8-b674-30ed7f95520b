<?php

declare(strict_types=1);

use App\Models\Country;
use App\Rules\NotExists;
use App\Services\DataMapperService;
use Illuminate\Database\Schema\Blueprint;

describe('Data Mapper Service Test', function () {
    it('can create a new data mapper', function () {
        Schema::create('countries', function (Blueprint $table) {
            $table->id();
            $table->string('code', 2)->unique('code');
            $table->string('iso3', 3)->unique('iso3');
            $table->systemname();
            $table->translatable('name');
            $table->is('deliverable');
            $table->is('favorite');
            $table->timestampsDefault();
        });
        Country::factory()->create([
            'code' => 'NL',
            'iso3' => 'NLD',
        ]);
        $mappings = [
            'exact_uniq_id' => [
                'key' => 'exact_uniq_id',
                'rules' => [
                    'required',
                    'string',
                    new NotExists('companies', 'exact_uniq_id'),
                ],
            ],
            'country_id' => [
                'key' => 'country_code',
                'rules' => 'required|integer|exists:countries,id',
                'before' => [
                    \App\Transformers\CountryCodeToCountryIdTransformer::class,
                ],
            ],
            'locale_id' => [
                'key' => 'country_code',
                'rules' => 'required|integer|exists:locales,id',
                'before' => [
                    \App\Transformers\CountryCodeToLocaleIdTransformer::class,
                ],
                'default' => 1,
            ],
            'name' => [
                'key' => 'name',
                'rules' => [
                    'required',
                    'string',
                    new NotExists('companies', 'name'),
                ],
            ],
            'email' => [
                'key' => 'email',
                'rules' => 'nullable|email:rfc,dns,spoof',
            ],
        ];

        $account = [
            "exact_id" => "22552",
            "exact_uniq_id" => "C7E48EB5-7470-4D3D-B291-059905DECF87",
            "uniq_id" => "C7E48EB5-7470-4D3D-B291-059905DECF87",
            "name" => "Autoschade Pijnaker",
            "country_code" => "NL",
            "city" => "AMSTELVEEN",
            "postal_code" => "1187 NR",
            "address" => "Legmeerdijk 25",
            "debitor_number" => "700145",
            "creditor_number" => null,
            "email" => null,
            "phone" => null,
        ];

        $dataMapper = new DataMapperService(
            $mappings,
            $account
        );


        expect($dataMapper->validated())->toMatchArray($account);
    });
});
