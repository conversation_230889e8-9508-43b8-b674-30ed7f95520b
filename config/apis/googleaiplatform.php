<?php

return [
    'protocol' => 'https',
    'host' => '{{config.apis.googleaiplatform.location}}-aiplatform.googleapis.com',

    'content' => 'json',
    'accept' => 'json',

    'authentication' => [
        'type' => 'google_auth',
        'credentials_file' => 'google_service_account.json',
        'scopes' => [
            'https://www.googleapis.com/auth/cloud-platform',
        ],
    ],

    'version' => 'v1',
    'project' => 'van-straaten-quickfix',
    'location' => 'europe-west4',
    'model' => 'gemini-1.5-pro-002',
    'publisher' => 'google',

    'timeout' => [
        'connect' => 3,
        'response' => 300,
    ],
    'retries' => [
        'attempts' => 0,
    ],

    'document_types' => [
        'Invoice',
        'Receipt',
        'Contract',
        'Report',
        'Quotation',
        'PurchaseOrder',
        'PurchaseOrderConfirmation',
        'DeliveryNote',
        'PackingSlip',
        'DataSheet',
        'Manual',
        'Presentation',
        'Certificate',
        'License',
        'InsurancePolicy',
        'CV',
        'Agreement',
        'Letter',
        'Other',
    ],
];
