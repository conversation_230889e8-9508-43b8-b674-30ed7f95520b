<?php

namespace App\Models\Filters;

use App\Models\Country;
use Illuminate\Support\Facades\URL;
use <PERSON>od<PERSON>\LaravelModelFilter\Enums\FilterMode;
use Lacodix\LaravelModelFilter\Filters\SelectFilter;

class CountryFilter extends SelectFilter
{
    public FilterMode $mode = FilterMode::CONTAINS;

    protected string $field = 'country_id';
    protected string $title = 'Country';

    public static ?array $optionsContent;

    public function options(): array
    {
        static $cached;

        if ($cached !== null) {
            return $cached;
        }

        // Get currently selected filter value(s)
        $selected = (array) ($this->values[$this->queryName()] ?? []);

        $options = Country::query()
            ->select(['id', 'systemname', 'code'])
            ->where(function ($query) use ($selected) {

                $query->whereIn('id', function ($sub) {
                    $sub
                        ->select($this->getQualifiedField())
                        ->from($this->model()->getTable())
                        ->distinct();
                });

                if (! empty($selected)) {
                    $query->orWhereIn('id', $selected);
                }
            })
            ->get()
        ;

        self::$optionsContent = $options
            ->mapWithKeys(fn ($country) => [
                $country->id => '
                    <div class="d-inline-flex align-items-center">
                        <img src="' . URL::asset('/img/flags/4x3/' . strtolower($country->code) . '.svg') . '" class="flag rounded d-block me-2" alt="Flag-' . strtoupper($country->code) . '">
                        <span>' . $country->systemname . '</span>
                    </div>
                ',
            ])
            ->toArray()
        ;

        return $cached = $options
            ->mapwithKeys(fn ($country) => [
                $country->systemname => $country->id,
            ])
            ->toArray()
        ;
    }
}
