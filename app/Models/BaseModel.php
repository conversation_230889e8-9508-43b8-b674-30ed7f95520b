<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Database\Eloquent\InvalidCastException;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Spatie\Regex\Regex;

class BaseModel extends Model
{
    // todo: remove this
    public array $originalValidatedData = [];

    public $timestamps = false;

    protected $dateFormat = 'Y-m-d H:i:s';

    public $systemlabelAttributes;

    protected $fillable = [];
    protected $translatable = [];
    protected $casts = [];
    protected $hidden = [
        'updated_at',
        'created_at',
    ];

    // Deprecated when complying with DB standards guid/hash ids
    protected $castsExceptions = [
        'salesforce_id',
        'exact_id',
        'uniq_id',
    ];

    // TODO: remove this
    protected $controller;

    // TODO: remove this
    public array $_dataAttributes = [];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        if ($this->usesTrait('\App\Traits\HasTranslations')) {
            $this->setTranslatables();
        }

        $this->setCasts();
    }

    public function toArray()
    {
        $attributes = parent::toArray();

        // check if dynamic attribute systemlabel is set
        if (isset($this->systemlabel)) {
            $attributes['systemlabel'] = $this->systemlabel;
        }

        if (in_array('created_at', $this->fillable)) {
            $attributes['created_at'] = $this->created_at;
        }

        if (in_array('updated_at', $this->fillable)) {
            $attributes['updated_at'] = $this->updated_at;
        }

        return $attributes;
    }

    public function fill(array $attributes)
    {
        $this->originalValidatedData = $attributes;

        return parent::fill($attributes);
    }

    /**
     * Get the system label for the model.
     * Used for select options and other places where a human readable label is needed.
     */
    public function getSystemlabelAttribute()
    {
        // set systemlabel
        $this->systemlabelAttributes ??= $this->hasColumn('systemname')
            ? 'systemname'
            : 'id'
        ;

        // normalize to array
        $this->systemlabelAttributes = ! is_array($this->systemlabelAttributes)
            ? [$this->systemlabelAttributes]
            : $this->systemlabelAttributes
        ;

        // get model values for attributes
        $values = [];
        foreach ($this->systemlabelAttributes as $attribute) {
            $values[] = $this->{$attribute} ?? null;
        }

        $values = array_filter($values);

        return implode(' - ', empty($values) ? ['[NEW]'] : $values);
    }

    /**
     * Check if the model uses a specific trait.
     *
     * @param string $trait The fully qualified class name of the trait.
     */
    public function usesTrait(string $trait): bool
    {
        return usesTrait($trait, $this);
    }

    public static function getMorphName()
    {
        return array_search(
            static::class,
            Relation::morphMap()
        );
    }

    public function getController()
    {
        if (! isset($this->controller)) {
            $this->controller = $this->getTable();
        }

        return $this->controller;
    }

    public function getFillables()
    {
        return $this->fillable;
    }

    public function getTranslatables()
    {
        return $this->translatable;
    }

    public function hasColumn($key)
    {
        if (! isset($this->columns)) {
            $this->columns = array_unique(array_merge(
                $this->getFillable(),
                array_keys($this->getCasts()),
                $this->getHidden()
            ));
        }

        return in_array($key, $this->columns);
    }

    public function getAttribute($key)
    {
        if (str_starts_with($key, 'days_')) {
            return str_split($this->{$key});
        }

        return parent::getAttribute($key);
    }

    public function setAttribute($key, $value)
    {
        if (str_starts_with($key, 'days_')) {
            return $this->{$key} = implode('', $value);
        }

        return parent::setAttribute($key, $value);
    }

    public function isEnumAttribute(string $attribute): bool
    {
        $casts = $this->getCasts();

        if (! isset($casts[$attribute]) ||
            strpos($casts[$attribute], '\\Enums\\') === false) {
            return false;
        }

        return true;
    }

    public function isBooleanAttribute(string $attribute): bool
    {
        $casts = $this->getCasts();

        if (! isset($casts[$attribute]) ||
            $casts[$attribute] != 'boolean') {
            return false;
        }

        return true;
    }

    public function resolveRouteBinding($value, $field = null)
    {

        if (! usesTrait('App\Traits\IsLockable', $this) ||
            ! method_exists($this, 'scopeWithIsLocked') ||
            ! in_array(request()->route()->getActionMethod(), ['edit', 'destroy', 'store'])) {
            return parent::resolveRouteBinding($value, $field);
        }

        return static::query()->withIsLocked()
            ->where($field ?? $this->getRouteKeyName(), $value)
            ->firstOrFail()
        ;
    }

    protected function setCasts()
    {
        foreach ($this->fillable as $column) {
            if (Regex::match('/^(is|has|can)_/', $column)->hasMatch()) {
                $this->casts[$column] = 'boolean';

                continue;
            }

            if (Regex::match('/_id$/', $column)->hasMatch() &&
                ! in_array($column, $this->castsExceptions)) {
                $this->casts[$column] = 'integer';

                continue;
            }

            if (Regex::match('/_at$/', $column)->hasMatch()) {
                $this->casts[$column] = 'datetime:Y-m-d H:i:s';

                continue;
            }

            // TODO: which DB standard ??
            if (Regex::match('/^price_?/', $column)->hasMatch()) {
                $this->casts[$column] = 'decimal:5';

                continue;
            }

            if (Regex::match('/^quantity_?/', $column)->hasMatch()) {
                $this->casts[$column] = 'integer';

                continue;
            }

            // TODO: which DB standard ??
            if (Regex::match('/^total_?/', $column)->hasMatch()) {
                $this->casts[$column] = 'decimal:2';

                continue;
            }
        }

        if (! in_array('updated_at', $this->casts) &&
            in_array('updated_at', $this->fillable)) {
            $this->casts['updated_at'] = 'datetime:Y-m-d H:i:s';
        }

        if (! in_array('created_at', $this->casts) &&
            in_array('created_at', $this->fillable)) {
            $this->casts['created_at'] = 'datetime:Y-m-d H:i:s';
        }
    }

    protected function isClassCastable($key)
    {
        $casts = $this->getCasts();

        if (! array_key_exists($key, $casts)) {
            return false;
        }

        $castType = $this->parseCasterClass($casts[$key]);

        if (in_array($castType, static::$primitiveCastTypes)) {
            return false;
        }

        // change here
        if (enum_exists($castType)) {
            return false;
        }

        if (class_exists($castType)) {
            return true;
        }

        throw new InvalidCastException($this->getModel(), $key, $castType);
    }

    protected function mutateAttributeForArray($key, $value)
    {
        if ($this->isClassCastable($key)) {
            $value = $this->getClassCastableAttributeValue($key, $value);
        } elseif (isset(static::$getAttributeMutatorCache[get_class($this)][$key]) &&
                  static::$getAttributeMutatorCache[get_class($this)][$key] === true) {
            $value = $this->mutateAttributeMarkedAttribute($key, $value);

            $value = $value instanceof DateTimeInterface
                        ? $this->serializeDate($value)
                        : $value;
        } else {
            $value = $this->mutateAttribute($key, $value);
        }

        // change here
        if ($this->isEnumCastable($key)) {
            $valueb = $this->getEnumCastableAttributeValue($key, $value);

            return $this->getStorableEnumValue($value, $valueb);
        }

        return $value instanceof Arrayable ? $value->toArray() : $value;
    }
}
