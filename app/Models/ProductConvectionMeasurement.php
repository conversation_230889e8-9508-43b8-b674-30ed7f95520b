<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;

class ProductConvectionMeasurement extends BaseModel
{
    protected $table = 'products_convections_measurements';

    protected $fillable = [
        'id',
        'product_convection_id',
        'type',
        'unit',
    ];
    protected $casts = [
        'id' => 'integer',
        'product_convection_id' => 'integer',
        'type' => 'string',
        'unit' => 'string',
        'updated_at' => 'datetime:d-m-Y H:i:s',
        'created_at' => 'datetime:d-m-Y H:i:s',
    ];

    public function getStepCountAttribute(): int
    {
        return $this->steps()->count();
    }

    public function steps(): HasMany
    {
        return $this->hasMany(ProductConvectionMeasurementStep::class, 'product_convection_measurement_id', 'id');
    }
}
