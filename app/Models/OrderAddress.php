<?php

namespace App\Models;

use App\Enums\AddressType;

class OrderAddress extends BaseModel
{
    protected $table = 'orders_addresses';

    protected $fillable = [
        'company_id',
        'address_id',
        'locale_id',
        'user_id',
        'type',
        'company',
        'salutation',
        'firstname',
        'lastname',
        'email',
        'phone',
    ];

    protected $casts = [
        'type' => AddressType::class,
    ];

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function address()
    {
        return $this->belongsTo(address::class);
    }
}
