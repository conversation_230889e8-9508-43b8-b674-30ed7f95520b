<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Slug extends BaseModel
{
    protected $fillable = [
        'id',
        'parent_id',
        'domain_id',
        'locale_id',
        'type',
        'sluggable_type',
        'sluggable_id',
        'slug',
    ];

    protected $casts = [];

    public function parent()
    {
        return $this->belongsTo(Slug::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(Slug::class, 'parent_id', 'id');
    }

    public function descendants()
    {
        return $this->children()->with('descendants');
    }

    public function siblings(): HasMany
    {
        return $this->hasMany(Slug::class, 'parent_id', 'parent_id')
            ->where([
                ['sluggable_type', '!=', $this->sluggable_type],
                ['sluggable_id', '!=', $this->sluggable_id],
            ]);
    }

    public function sluggable(): morphTo
    {
        return $this->morphTo();
    }
}
