<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\belongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

class Address extends BaseModel
{
    protected $table = 'addresses';

    protected $fillable = [
        'country_id',
        'street',
        'housenumber',
        'addition',
        'postalcode',
        'city',
        'department',
        'state',
        'is_verified',
    ];

    public function country(): HasOne
    {
        return $this->hasOne(Country::class, 'id');
    }

    public function relations(): MorphToMany
    {
        return $this->morphToMany(RelatedAddress::class, 'relatable', 'addresses_relations');
    }
}

 // public function set() {

    // 	if (!isset($this->postalcode) ||
    // 		!isset($this->city) ||
    // 		!isset($this->street) ||
    // 		!isset($this->housenumber) ||
    // 		!isset($this->addition) ||
    // 		!isset($this->country_id)) {
    // 		return false;
    // 	}

    // 	return $this->save();
    // }

    // public function orders() :belongsToMany
    // {
    //     return $this->belongsToMany(Order::class, 'orders_addresses', 'address_id', 'order_id')
    //         ->withPivot(
    //             'type',
    //             'company',
    //             'gender',
    //             'firstname',
    //             'lastname',
    //             'phone',
    //             'email'
    //         );
    // }

    // public function companies(): belongsToMany
    // {
    //     return $this->belongsToMany(Company::class, 'companies_addresses')
    //         ->withPivot('id' ,'type', 'language', 'company', 'gender', 'firstname', 'lastname', 'email', 'phone', 'is_default', 'is_deleted');
    // }

    // public function users(): BelongsToMany
    // {
    //     return $this->belongsToMany(User::class, 'company_addresses_users');
    // }

    // public function country(): BelongsTo
    // {
    //     return $this->belongsTo(Country::class);
    // }

    // public function getAddress()
    // {
    //     return implode(' ', [$this->street, $this->housenumber]);
    // }

    // public function getPostalcodeAndStreet()
    // {
    //     return implode(' ', [$this->postalcode, $this->city]);
    // }
