<?php

namespace App\Models;

use App\Traits\HasTranslations;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Country extends BaseModel
{
    use Cachable,
        HasTranslations,
        HasFactory
    ;

    protected $fillable = [
        'code',
        'iso3',
        'systemname',
        'is_deliverable',
        'is_favorite',
    ];

    protected $translatable = [
        'name',
    ];

    protected $casts = [];

}
