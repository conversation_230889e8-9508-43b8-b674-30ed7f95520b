<?php

namespace App\Models;

use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LedgerAccount extends BaseModel
{
    use Cachable;

    public $table = 'ledgers_accounts';

    protected $fillable = [
        'id',
        'ledger_id',
        'ledger_category_id',
        'exact_id',
        'systemname',
        'code',
    ];

    public $systemlabelAttributes = [
        'code',
        'systemname',
    ];

    public function ledger(): BelongsTo
    {
        return $this->belongsTo(ItemGroup::class, 'ledger_id');
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(LedgerCategory::class, 'ledger_category_id');
    }
}
