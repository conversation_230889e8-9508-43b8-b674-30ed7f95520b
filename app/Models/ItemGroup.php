<?php

namespace App\Models;

use App\Traits\HasLedgerAccounts;
use App\Traits\IsLockable;
use App\Traits\IsSortable;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class ItemGroup extends BaseModel
{
    use Cachable;
    use HasLedgerAccounts;
    use IsLockable;
    use IsSortable;

    public $table = 'items_groups';

    protected $fillable = [
        'id',
        'systemname',
    ];

    protected $sortable = [
        'systemname',
        'items_count',
        'products_count',
    ];

    protected $lockable = [];

    protected $lockableRelations = [
        'items',
    ];

    public function items(): HasMany
    {
        return $this->hasMany(Item::class, 'item_group_id');
    }

    public function products(): HasManyThrough
    {
        return $this->hasManyThrough(
            Product::class,
            Item::class,
            'item_group_id', // Foreign key on items table...
            'item_id',       // Foreign key on products table...
            'id',            // Local key on item_groups table...
            'id'             // Local key on items table...
        );
    }
}
