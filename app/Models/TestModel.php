<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TestModel extends Model
{
    protected static function booting()
    {

        $parentClass = get_parent_class(static::class);

        \Log::debug('TestModel-' . $parentClass);

        parent::booted(function ($model) {
            exit('111111');
            \Log::debug('asfasd');

            // $model->fillable ??= [];
            // $model->casts ??= [];

            // $model->setCasts();
        });

        parent::booting();
    }
}
