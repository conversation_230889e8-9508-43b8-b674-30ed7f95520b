<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Ledger extends BaseModel
{
    public $table = 'ledgers';

    protected $fillable = [
        'id',
        'systemname',
    ];

    public function getFullNameAttribute()
    {
        return 'fdgdgd';
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function categories(): HasMany
    {
        return $this->hasMany(LedgerCategory::class, 'ledger_id');
    }

    public function accounts(): HasMany
    {
        return $this->hasMany(LedgerAccount::class, 'ledger_id');
    }

    public static function getSelectOptions($selected = null)
    {
        return LedgerCategory::getOptionsList(
            LedgerCategory::getStructuredList(
                with: ['accounts'],
              //  wheres: [ 'ledger_id' => $company->ledger->id ]
            ),
            descendants: ['descendants', 'accounts'],
            disabled: [
                'model' => 'App\Models\LedgerCategory',
            ],
            label: 'systemlabel',
            prefix: false,
            selected: $selected
        );
    }
}
