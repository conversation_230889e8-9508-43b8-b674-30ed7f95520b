<?php

namespace App\Models;

use App\Enums\InputType;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Settings extends BaseModel
{
    protected $fillable = [
        'systemname',
        'type',
        'key',
        'value',
    ];

    protected $casts = [
        'type' => InputType::class,
    ];

    protected $translatable = [
        'name',
        'description',
    ];

    public function settingable(): MorphTo
    {
        return $this->morphTo();
    }

    // public function getAttribute($key)
    // {
    //     if ($key === 'value') {
    //         return isset($this->pivot)
    //             ? $this->pivot->value
    //             : parent::getAttribute($key)
    //         ;
    //     }

    //     return parent::getAttribute($key);
    // }

    // public function getValueAttribute()
    // {
    //     return isset($this->pivot)
    //         ? $this->pivot->value
    //         : $this->getAttribute('value')
    //     ;
    // }

    // public function getDefaultValueAttribute()
    // {
    //     return $this->getAttributeFromArray('value');
    // }

}
