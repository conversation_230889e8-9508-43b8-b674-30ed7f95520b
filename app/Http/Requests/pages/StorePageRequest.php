<?php

namespace App\Http\Requests\pages;

use App\Services\SlugService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;

class StorePageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    protected function prepareForValidation(): void {}

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {

        $rules = [
            'parent_id' => ['nullable', 'integer'],
            'hero_header_ids' => ['array'],
            'hero_footer_ids' => ['array'],
            'type' => ['string', 'nullable'],
            'icon' => ['string', 'nullable'],
            'systemname' => [
                'bail',
                'required',
                'string',
                'min:3',
                'max:256',
                Rule::unique('pages', 'systemname')->ignore($this->input('id')),
            ],

            'in_use' => ['boolean'],
            'is_selectable' => ['boolean'],
            'is_editable' => ['boolean'],
            'is_locked' => ['boolean'],
            'is_active' => ['boolean'],

            'has_image' => ['boolean'],
            'has_textblock' => ['boolean'],
            'has_widget' => ['boolean'],
            'has_sidebar' => ['boolean'],
            'has_usps' => ['boolean'],
            'has_related' => ['boolean'],

            'sort_menu' => ['integer'],
            'sort_menu_top' => ['integer'],
            'sort_menu_1' => ['integer'],
            'sort_menu_2' => ['integer'],
            'sort_menu_footer' => ['integer'],
            'sort_menu_dashboard' => ['integer'],
            'image' => ['file', 'image'],
        ];

        foreach (config('locales') as $locale => $localeData) {
            $rules["is_active_" . $locale] = "boolean";
            $rules["is_indexable_" . $locale] = "boolean";
            $rules["title_" . $locale] = "required_if:is_active_" . $locale . ",1|string|nullable";
            $rules["title_tab_" . $locale] = 'string|nullable';
            $rules["title_sub_" . $locale] = 'string|nullable';
            $rules["title_custom_" . $locale] = 'string|nullable';
            $rules["content_" . $locale] = 'string|nullable';
            $rules["content_intro_" . $locale] = 'string|nullable';
            $rules["content_top_" . $locale] = 'string|nullable';
            $rules["content_bottom_" . $locale] = 'string|nullable';
            $rules["meta_description_" . $locale] = 'string|nullable';
            $rules["meta_keywords_" . $locale] = 'string|nullable';
        }

        // Check if the model uses the Sluggable trait via the SlugService
        // if (SlugService::modelUsesSluggable($model)) {

        //     dd('has sluggable ');
        //     $rules['slug'] = [
        //         'required',
        //         'string',
        //         'max:255',
        //         Rule::unique('your_models_table')->ignore($model->id),
        //         function ($attribute, $value, $fail) use ($model) {
        //             // Add custom validation logic if needed
        //             if (Str::slug($value) !== $value) {
        //                 $fail('The :attribute must be a valid slug.');
        //             }
        //         },
        //     ];
        // }

        return $rules;
    }

    /**
     * Configure the validator instance.
     */
    // public function withValidator(Validator $validator): void
    // {
    //     $route = $this->route();

    //     if (!SlugService::modelUsesSluggable($route->controller->getConfig()['model'])) {
    //         return;
    //     }

    //     $className = "App\\Models\\".Str::ucfirst($route->controller->getConfig()['model']);
    //     $model = (new $className)->fill($this->request->all());

    //     $slugService = (new SlugService())->slug($model);

    //     dd($slugService->validate());

    //     $validator->after(function ($validator) {

    //        // if ($this->age < 18) {
    //             $validator->errors()->add(
    //                 'slug',
    //                 'd asdsa adas.'
    //             );
    //         //}
    //     });
    // }

    public function messages()
    {
        return [
            'systemname.required' => 'The system name is required.',
            'sorting.required' => 'The sorting is required.',
            'systemname.string' => 'The system name must be a string.',
            'systemname.min' => 'The system name must be at least :min characters.',
            'systemname.max' => 'The system name must not exceed :max characters.',
            'systemname.unique' => 'The system name is already taken.',
            'status.boolean' => 'The status field must be a boolean.',
            'is_highlighted.boolean' => 'The is_highlighted field must be a boolean.',
            'published_at.required' => 'The published at field is required.',
            'published_at.date' => 'The published at field must be a valid date.',
            'expired_at.required' => 'The expired at field is required.',
            'expired_at.date' => 'The expired at field must be a valid date.',
            'type_id.required' => 'The type field is required.',
            'type_id.string' => 'The type field must be a string.',
            'status_nl.boolean' => 'The status_nl field must be a boolean.',
            'status_de.boolean' => 'The status_de field must be a boolean.',
            'status_en.boolean' => 'The status_en field must be a boolean.',
            'title_nl.required' => 'The title (NL) field is required when status_nl is true.',
            'title_de.required' => 'The title (DE) field is required when status_de is true.',
            'title_en.required' => 'The title (EN) field is required when status_en is true.',
            'categories.required' => 'Categories are required if you dont check globally available.',

        ];
    }
}
