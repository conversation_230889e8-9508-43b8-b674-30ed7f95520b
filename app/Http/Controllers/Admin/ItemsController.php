<?php

namespace App\Http\Controllers\Admin;

use App\Enums\LedgerAccountType;
use App\Enums\SystemContext;
use App\Http\Controllers\ModuleController;
use App\Http\Requests\StoreItemRequest;
use App\Models\Company;
use App\Models\Item;
use App\Models\ItemGroup;
use App\Models\LedgerCategory;
use App\Models\Property;
use App\Models\Unit;
use App\Models\UnitGroup;
use Arr;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;

class ItemsController extends ModuleController
{
    public function index(): View
    {
        return view('admin::pages.items.index', [
            'data' => Item::query()
                ->where('items.company_id', Company::main())
                ->withCount('products', 'relatedBy')
                ->with([
                    'group',
                    'units',
                    'units.unitGroupUnit',
                    'units.unitGroupUnit.unit',
                ])
                ->withIsLocked()
                ->applyFilter()
                ->applySort()
                ->get(),
        ]);
    }

    public function create(): View
    {
        return $this->edit(new Item());
    }

    public function edit(
        Item $item
    ): View {

        $item->load([
            'units',
            'units.relations',
            'units.unitGroupUnit',
            'units.unitGroupUnit.unit',
            'units.unitGroupUnit.unit.compositionals',
            'ledgerAccounts',
        ]);

        $properties = Property::query()
            ->where('system_context', SystemContext::UNIT)
            ->with('values')
            ->get()
        ;

        $unitGroups = UnitGroup::query()
            ->with([
                'units',
                'units.unit',
                'units.unit.compositionals',
            ])
            ->get()
        ;

        $unitGroupWeightUnits = Unit::query()
            ->whereNot('unit_type_id', 4)
            ->get()
        ;

        $this->bladeService->addToDataSet('properties', $properties);
        $this->bladeService->addToDataSet('unit_groups', $unitGroups);

        return view('admin::pages.items.create', [
            'data' => $item,
            'companyId' => Company::main(),
            'unitGroups' => $unitGroups,
            'unitGroupWeightUnits' => $unitGroupWeightUnits,
            'properties' => $properties,
            'itemGroups' => ItemGroup::all(),
            'ledgerAccountTypes' => LedgerAccountType::cases(),
            'ledgerAccountsOptions' => Arr::toOptions(
                LedgerCategory::query()
                    ->with('descendants.accounts')
                    ->where('parent_id', null)
                    ->get(),
                'id',
                'systemlabel', [
                    'descendants',
                    'accounts',
                ]
            ),
        ]);
    }

    public function store(
        StoreItemRequest $request
    ): RedirectResponse {

        $item = DB::transaction(function () use ($request) {

            $validated = $request->validated();

            $item = Item::query()
                ->withIsLocked()
                ->updateOrCreate([
                'id' => $validated['id'],
            ], [
                'item_group_id' => $validated['item_group_id'],
                'company_id' => $validated['company_id'],
                'code' => $validated['code'],
                'systemname' => $validated['systemname'],
                'type' => $validated['type'],
                'is_custom' => false, // TODO: is for if item is directy 'custom' added/created at purchaseorder
                'ledger_account' => $validated['ledger_account'] ?? null,
            ]);

            if (! $item->wasRecentlyCreated && (
                    $item->wasChanged('type') ||
                    $item->wasChanged('item_group_id')
                )
            ) {
                // update related items
                $item->relatedBy()->update([
                    'item_group_id' => $validated['item_group_id'],
                    'type' => $validated['type'],
                ]);
            }

            $itemVariant = $item
                ->variants()
                ->withIsLocked()
                ->updateOrCreate([
                    'item_id' => $item->id,
                ], [
                    'code' => $validated['code'],
                    'systemname' => $validated['systemname'],
                ])
            ;

            if ($item->is_locked) {
                return $item;
            }

            $itemUnitIds = [];
            $itemVariantUnitIds = [];
            foreach ($validated['units'] as $unit) {

                $itemUnit = $item
                    ->units()
                    ->withIsLocked()
                    ->updateOrCreate([
                        'id' => strpos($unit['id'], '-') !== false ? null : $unit['id'],
                        'unit_group_id' => $unit['unit_group_id'],
                    ], [
                        'unit_group_unit_id' => $unit['unit_group_unit_id'],
                        'value' => $unit['value'] ?? null,
                    ])
                ;
                $itemUnitIds[] = $itemUnit->id;

                $itemVariantUnit = $itemVariant
                    ->units()
                    ->withIsLocked()
                    ->updateOrCreate([
                    'unit_group_id' => $unit['unit_group_id'],
                ], [
                    'unit_group_unit_id' => $unit['unit_group_unit_id'],
                    'value' => $unit['value'] ?? null,
                ]);
                $itemVariantUnitIds[] = $itemVariantUnit->id;

                $itemUnitRelationIds = [];
                $itemVariantUnitRelationIds = [];
                $itemVariantUnitRelation = [];
                foreach (($unit['relations'] ?? []) as $relation) {

                    if (is_null($relation) || is_null($relation['model'])) {
                    continue;
                    }

                    [$relatableType, $relatableId] = explode('::', $relation['model']);

                    $itemUnitRelation = $itemUnit->relations()->withIsLocked()->updateOrCreate([
                        'id' => strpos($relation['id'], '-') !== false ? null : $relation['id'],
                    ], [
                        'relatable_type' => $relatableType,
                        'relatable_id' => intval($relatableId),
                    ]);
                    $itemUnitRelationIds[] = $itemUnitRelation->id;

                    $itemVariantUnitRelation = $itemVariantUnit->relations()->withIsLocked()->updateOrCreate([
                        'relatable_type' => $relatableType,
                        'relatable_id' => intval($relatableId),
                    ]);
                    $itemVariantUnitRelationIds[] = $itemVariantUnitRelation->id;
                }

                // delete itemUnitRelations that are not in the request
                $itemUnit->relations()
                    ->withIsLocked()
                    ->whereNotIn('id', $itemUnitRelationIds)
                    ->delete()
                ;

                // delete itemVariantUnitRelations that are not in the request
                $itemVariantUnit->relations()
                    ->withIsLocked()
                    ->whereNotIn('id', $itemVariantUnitRelationIds)
                    ->delete()
                ;

                // delete itemUnits that are not in the request
                $itemVariant->units()
                    ->withIsLocked()
                    ->whereNotIn('id', $itemVariantUnitIds)
                    ->delete()
                ;
            }

            // delete itemUnits that are not in the request
            $item->units()
                ->withIsLocked()
                ->whereNotIn('id', $itemUnitIds)
                ->delete()
            ;

            // delete itemVariants that are not in the request
            $item->variants()
                ->withIsLocked()
                ->whereNot('id', $itemVariant->id)
                ->delete()
            ;

            return $item;

        }, 2);

        $this->setMessage($item);

        return to_route('admin.items.index');
    }

    public function destroy(
        Item $item
    ): RedirectResponse {
        if ($item->is_locked) {
            session()->flash('error', 'Item can not be deleted');

            return back();
        }

        $item->delete();

        session()->flash('message', 'Item deleted successfully');

        return to_route('admin.items.index');
    }
}
