<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ModuleController;
use App\Http\Requests\StoreItemGroupRequest;
use App\Models\ItemGroup;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;

class ItemGroupsController extends ModuleController
{
    public function index(): View
    {
        return view('admin::pages.itemgroups.index', [
            'data' => ItemGroup::query()
                ->withCount('items', 'products')
                ->withIsLocked()
                ->applySort()
                ->get(),
        ]);
    }

    public function create(): View
    {
        return $this->edit(new ItemGroup());
    }

    public function edit(
        ItemGroup $itemGroup
    ): View {
        return view('admin::pages.itemgroups.create', [
            'data' => $itemGroup,
        ]);
    }

    public function store(
        StoreItemGroupRequest $request
    ): RedirectResponse {
        $validated = $request->validated();

        $itemGroup = ItemGroup::withIsLocked()->updateOrCreate([
            'id' => $validated['id'],
        ], $validated);

        $this->setMessage($itemGroup);

        return to_route('admin.itemgroups.index');
    }

    public function destroy(
        ItemGroup $itemGroup
    ): RedirectResponse {
        if ($itemGroup->is_locked) {
            $this->setError('ITEM GROUP cannot be deleted.');

            return to_route('admin.itemgroups.index');
        }

        $itemGroup->delete();

        $this->setMessage($itemGroup);

        return to_route('admin.itemgroups.index');
    }
}
