<?php

namespace App\Http\Controllers\Portal;

use App\Http\Controllers\ModuleController;
use App\Models\Product;
use App\Models\Property;
use App\Models\Usp;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;

class ProductsController extends ModuleController
{
    public function index(Request $request): View
    {

        $sort = ['sort', 'desc'];

        $filters = [
            [
                'id' => 1,
                'title' => 'Categories',
                'is_open' => true,
                'is_static' => true,
                'type' => 'category',
                'values' => [
                    [
                        'id' => 2,
                        'title' => 'Fabrics',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 3,
                        'title' => 'Panels',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 4,
                        'title' => 'Stickers',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 5,
                        'title' => 'SEG',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 6,
                        'title' => 'BeMatrix',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 7,
                        'title' => 'AluVision',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                ],
            ],
            [
                'id' => 2,
                'title' => 'Color',
                'is_open' => false,
                'is_static' => false,
                'type' => 'value',
                'values' => [
                    [
                        'id' => 2,
                        'title' => 'red',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 3,
                        'title' => 'blue',
                        'quantity' => 3,
                        'selected' => true,
                    ],
                ],
            ],
            [
                'id' => 3,
                'title' => 'Roll width',
                'is_open' => false,
                'is_static' => false,
                'type' => 'value',
                'values' => [
                    [
                        'id' => 2,
                        'title' => '106 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 3,
                        'title' => '124 cm',
                        'quantity' => 2,
                        'selected' => true,
                    ],
                    [
                        'id' => 4,
                        'title' => '137 cm',
                        'quantity' => 100,
                        'selected' => true,
                    ],
                    [
                        'id' => 5,
                        'title' => '152 cm',
                        'quantity' => 10,
                        'selected' => true,
                    ],
                ],
            ],
            [
                'id' => 3,
                'title' => 'Roll width',
                'is_open' => false,
                'is_static' => false,
                'type' => 'value',
                'values' => [
                    [
                        'id' => 2,
                        'title' => '106 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 3,
                        'title' => '124 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 4,
                        'title' => '137 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 5,
                        'title' => '152 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                ],
            ],
            [
                'id' => 3,
                'title' => 'Roll width',
                'is_open' => false,
                'is_static' => false,
                'type' => 'value',
                'values' => [
                    [
                        'id' => 2,
                        'title' => '106 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 3,
                        'title' => '124 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 4,
                        'title' => '137 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 5,
                        'title' => '152 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                ],
            ],
            [
                'id' => 3,
                'title' => 'Roll width',
                'is_open' => false,
                'is_static' => false,
                'type' => 'value',
                'values' => [
                    [
                        'id' => 2,
                        'title' => '106 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 3,
                        'title' => '124 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 4,
                        'title' => '137 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 5,
                        'title' => '152 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                ],
            ],
            [
                'id' => 3,
                'title' => 'Roll width',
                'is_open' => false,
                'is_static' => false,
                'type' => 'value',
                'values' => [
                    [
                        'id' => 2,
                        'title' => '106 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 3,
                        'title' => '124 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 4,
                        'title' => '137 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 5,
                        'title' => '152 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                ],
            ],
            [
                'id' => 3,
                'title' => 'Roll width',
                'is_open' => false,
                'is_static' => false,
                'type' => 'value',
                'values' => [
                    [
                        'id' => 2,
                        'title' => '106 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 3,
                        'title' => '124 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 4,
                        'title' => '137 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 5,
                        'title' => '152 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                ],
            ],
            [
                'id' => 3,
                'title' => 'Roll width',
                'is_open' => false,
                'is_static' => false,
                'type' => 'value',
                'values' => [
                    [
                        'id' => 2,
                        'title' => '106 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 3,
                        'title' => '124 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 4,
                        'title' => '137 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 5,
                        'title' => '152 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                ],
            ],
            [
                'id' => 3,
                'title' => 'Roll width',
                'is_open' => false,
                'is_static' => false,
                'type' => 'value',
                'values' => [
                    [
                        'id' => 2,
                        'title' => '106 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 3,
                        'title' => '124 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 4,
                        'title' => '137 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 5,
                        'title' => '152 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                ],
            ],
            [
                'id' => 3,
                'title' => 'Roll width',
                'is_open' => false,
                'is_static' => false,
                'type' => 'value',
                'values' => [
                    [
                        'id' => 2,
                        'title' => '106 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 3,
                        'title' => '124 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 4,
                        'title' => '137 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 5,
                        'title' => '152 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                ],
            ],
            [
                'id' => 3,
                'title' => 'Roll width',
                'is_open' => false,
                'is_static' => false,
                'type' => 'value',
                'values' => [
                    [
                        'id' => 2,
                        'title' => '106 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 3,
                        'title' => '124 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 4,
                        'title' => '137 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 5,
                        'title' => '152 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                ],
            ],
            [
                'id' => 3,
                'title' => 'Roll width',
                'is_open' => false,
                'is_static' => false,
                'type' => 'value',
                'values' => [
                    [
                        'id' => 2,
                        'title' => '106 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 3,
                        'title' => '124 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 4,
                        'title' => '137 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 5,
                        'title' => '152 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                ],
            ],
            [
                'id' => 3,
                'title' => 'Roll width',
                'is_open' => false,
                'is_static' => false,
                'type' => 'value',
                'values' => [
                    [
                        'id' => 2,
                        'title' => '106 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 3,
                        'title' => '124 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 4,
                        'title' => '137 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 5,
                        'title' => '152 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                ],
            ],
            [
                'id' => 3,
                'title' => 'Roll width',
                'is_open' => false,
                'is_static' => false,
                'type' => 'value',
                'values' => [
                    [
                        'id' => 2,
                        'title' => '106 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 3,
                        'title' => '124 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 4,
                        'title' => '137 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 5,
                        'title' => '152 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                ],
            ],
            [
                'id' => 3,
                'title' => 'Roll width',
                'is_open' => false,
                'is_static' => false,
                'type' => 'value',
                'values' => [
                    [
                        'id' => 2,
                        'title' => '106 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 3,
                        'title' => '124 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 4,
                        'title' => '137 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                    [
                        'id' => 5,
                        'title' => '152 cm',
                        'quantity' => 50,
                        'selected' => true,
                    ],
                ],
            ],
        ];

        if (isset($request->sort)) {
            session(['products.sort' => $request->sort]);
        }

        if (isset(session('products')['sort'])) {
            $sort = explode('.', session('products')['sort']);
        }

        $data = Product::orderBy($sort[0], $sort[1])->where('status', 1)->get();

        return view('portal.pages.products.index', compact('data', 'filters', 'sort'));
    }

    public function specific(Product $product): View
    {
        $uspIds = $product->attachable->where('linkable_type', 'Usp')->pluck('linkable_id');

        $usps = Usp::whereIn('id', $uspIds)->get();

        $propertiesValues = $product->propertyValues;
        $propertyIds = $propertiesValues->pluck('property_id')->toArray();
        $properties = Property::whereIn('id', $propertyIds)->get();

        return view('portal.pages.products.specific', compact('product', 'properties', 'propertiesValues', 'usps'));
    }
}
