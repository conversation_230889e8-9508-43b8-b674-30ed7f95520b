<?php

namespace App\View\Components\Admin\Component\Form;

use App\Services\FormService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ViewErrorBag;

class Form extends Component
{
    /**
     * Form method spoofing to support PUT, PATCH and DELETE actions.
     * https://laravel.com/docs/master/routing#form-method-spoofing
     */

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct(
        public null|array|Collection|Model $bind = null,
        public bool $spoofMethod = false,
      //  public string $action = '',
        public string $method = 'POST'
    ) {
        $this->method = strtoupper($method);

        $this->spoofMethod = in_array($this->method, ['PUT', 'PATCH', 'DELETE']);
        app(FormService::class)->bindTarget($bind);
    }

    /**
     * Returns a boolean wether the error bag is not empty.
     *
     * @param string $bag
     */
    public function hasError($bag = 'default'): bool
    {
        $errors = View::shared('errors', function () {
            return request()->session()->get('errors', new ViewErrorBag());
        });

        return $errors->getBag($bag)->isNotEmpty();
    }
}
