<?php

namespace App\View\Components\Admin\Component\Form;

use App\Services\FormService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class Col extends Component
{
    public function __construct(
        public null|array|Collection|Model $bind = null,
        public null|bool|string $locale = null,
        public bool $withinCol = true
    ) {
        app(FormService::class)->bindTarget($bind);
        app(FormService::class)->setLocale($locale);
    }
}
