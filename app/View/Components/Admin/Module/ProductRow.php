<?php

namespace App\View\Components\Admin\Module;

use Illuminate\View\Component;
use Illuminate\View\View;

class ProductRow extends Component
{
    public $group;

    public function __construct(public $row, public $count)
    {

        $this->group = $row->is_convection == 1
            ? 'Convection'
            : ($row->is_printable == 1
                ? 'Substrate'
                : ($row->type == 'end'
                    ? 'Endproduct'
                    : 'Accessory'));

    }

    public function render(): View
    {
        return view('admin::components.modules.product_row');
    }
}
