<?php

namespace App\Console\Commands;

use App\IntegrationMapper\ProviderRegistry;
use App\Rules\NotExists;
use App\Services\APIS\ExactGlobeAPIService;
use App\Services\DataMapperService;
use Illuminate\Console\Command;

class TestCommand extends Command
{
    protected $signature = 'app:test';

    public function handle(): void
    {
//        $registry = new ProviderRegistry();
//
//        $provider = $registry->getProvider('exact');
//
//        $provider->fromArray([
//            'name' => '<PERSON>',
//            'email' => '<EMAIL>',
//            'phone' => '06********',
//            'country' => 'NL',
//            'city' => 'Amsterdam',
//            'postalCode' => '1011 AA',
//            'address' => 'Kerkstraat 1',
//            'debitorNumber' => '********',
//            'creditorNumber' => '********',
//        ]);
//
//        $provider->toDatabase();

        $service = new ExactGlobeAPIService();
        $account = $service->getAccount('C7E48EB5-7470-4D3D-B291-059905DECF87');

        $mappings = [
            'exact_uniq_id' => [
                'key' => 'exact_uniq_id',
                'rules' => [
                    'required',
                    'string',
                    new NotExists('companies', 'exact_uniq_id'),
                ],
            ],
            'country_id' => [
                'key' => 'country_code',
                'rules' => 'required|integer|exists:countries,id',
                'before' => [
                    \App\Transformers\CountryCodeToCountryIdTransformer::class,
                ],
            ],
            'locale_id' => [
                'key' => 'country_code',
                'rules' => 'required|integer|exists:locales,id',
                'before' => [
                    \App\Transformers\CountryCodeToLocaleIdTransformer::class,
                ],
                'default' => 1,
            ],
            'name' => [
                'key' => 'name',
                'rules' => [
                    'required',
                    'string',
                    new NotExists('companies', 'name'),
                ],
            ],
            'email' => [
                'key' => 'email',
                'rules' => 'nullable|email:rfc,dns,spoof',
            ],
        ];
        dd((array) $account);

        $companyMapper = new DataMapperService(
            $mappings,
            $account
        );
        dump($companyMapper->hasErrors());
    }
}
