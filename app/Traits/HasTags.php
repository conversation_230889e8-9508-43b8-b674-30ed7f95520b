<?php

namespace App\Traits;

use App\Models\Tag;
use App\Observers\TaggableObserver;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

trait HasTags
{
    public function tags(): MorphToMany
    {
        return $this->morphToMany(Tag::class, 'taggable', 'tags_relations');
    }

    // public static function bootHasTags(): void
    // {
    //     static::observe(app(TaggableObserver::class));
    // }

    protected static function bootHasTags()
    {
        static::saved(function (Model $model) {
            $request = request();

            $tagList = [];
            foreach (config('locales') as $locale => $localeData) {

                $input = 'meta_keywords_' . $locale;

                if (! $request->has($input)) {
                    continue;
                }

                foreach (json_decode($request->input($input), true) as $tag) {
                    $tagList[] = [
                        'locale_id' => $localeData['id'],
                        'name' => trim(strtolower($tag['name'])),
                    ];
                }
            }

            Tag::upsert(
                $tagList,
                ['locale_id', 'name'],
                ['name']
            );

            $tags = Tag::whereIn('name', array_unique(array_column($tagList, 'name')))->get();

            $tagIds = [];
            foreach ($tagList as $tag) {
                $tagIds[] = $tags
                    ->where('locale_id', $tag['locale_id'])
                    ->where('name', $tag['name'])->first()->id;
            }

            $model->tags()->detach();
            $model->tags()->attach($tagIds);
        });
    }
}
