<?php

namespace App\Traits;

use App\Enums\PropertySystemUsage;
use App\Models\Property;
use App\Models\PropertyValue;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

trait HasPropertyValues
{
    public function propertyValues(): MorphToMany
    {
        return $this->morphToMany(
            PropertyValue::class,
            'relatable',
            'properties_values_relations'
        );
    }

    protected static function bootHasPropertyValues()
    {
        static::saved(function (Model $model) {
            $request = request();

            $model->propertyValues()->detach();

            if (! $request->has('properties')) {
                return;
            }

            $propertyValues = [];
            foreach ($request->input('properties') as $idx => $values) {

                if (is_null($values)) {
                    continue;
                }

                $values = is_array($values)
                    ? $values
                    : [$values]
                ;

                $propertyId = $idx;
                if (PropertySystemUsage::has($idx)) {
                    if (($property = Property::select('id')->where('system_usage', PropertySystemUsage::fromName($idx)?->value)?->first()) === null) {
                        continue;
                    }

                    $propertyId = $property->id;
                }

                foreach ($values as $valueId) {
                    $propertyValues[] = [
                        'property_id' => $propertyId,
                        'property_value_id' => $valueId,
                    ];
                }
            }

            $model->propertyValues()->attach($propertyValues);

        });
    }
}
