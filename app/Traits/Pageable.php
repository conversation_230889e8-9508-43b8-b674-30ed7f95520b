<?php

namespace App\Traits;

trait Pageable
{
    use HasTags;

    protected function initializePageable()
    {
        $this->setPageables();
    }

    private function setPageables()
    {
        $this->fillable = array_merge(
            $this->fillable ?? [], [
                'controllable_type',
                'controllable_method',
                'is_active',
                'is_auth',
        ]);

        $this->translatable = array_merge(
            $this->translatable ?? [], [
            'title',
            'title_sub',
            'title_tab',
            'content',
            'content_intro',
            'content_top',
            'content_bottom',
            'meta_description',
        //    'meta_keywords',
            'is_indexable',
            'is_active',
        ]);
    }
}
