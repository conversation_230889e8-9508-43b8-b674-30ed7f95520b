<?php

namespace App\Enums;

use App\Traits\Enum;

enum PropertySystemUsage: string
{
    use Enum;

    const DEFAULT = self::PRODUCTS;

    case PRODUCTS = 'products';

    case PAYMENT_CONDITION_DAYS = 'payments.conditions.days';

    case ITEM_UNITTYPE_ROLL_WIDTH = 'item.unittype.roll.width';
    case ITEM_UNITTYPE_ROLL_LENGTH = 'item.unittype.roll.length';
    case ITEM_UNITTYPE_ROLL_WEIGHT = 'item.unittype.roll.weight';

    case ITEM_UNITTYPE_PLATE_WIDTH = 'item.unittype.plate.width';
    case ITEM_UNITTYPE_PLATE_HEIGHT = 'item.unittype.plate.height';
    case ITEM_UNITTYPE_PLATE_LENGTH = 'item.unittype.plate.length';
}
