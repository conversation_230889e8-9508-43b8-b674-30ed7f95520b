<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Foundation\Queue\Queueable;
use Mail;

class sentMail implements ShouldBeUnique, ShouldQueue
{
    use Queueable;

    public function __construct(
        public string $relatableModelClass,
        public int $relatableModelId,
        public string $mailable
    ) {}

    public function handle(): void
    {
        try {

            $model = new (Relation::getMorphedModel($this->relatableModelClass))();
            $model = $model->findOrFail($this->relatableModelId);

            Mail::send(new ($this->mailable)($model));

        } catch (\Exception $e) {
            $this->fail($e->getMessage());
        }
    }

    public function uniqueId(): string
    {
        return $this->relatableModelClass . $this->relatableModelId . $this->mailable;
    }
}
