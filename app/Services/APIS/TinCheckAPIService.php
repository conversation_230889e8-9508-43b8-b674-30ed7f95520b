<?php

namespace App\Services\APIS;

use App\Enums\TaxType;
use Spatie\Regex\Regex;

class TinCheckAPIService extends APIService
{
    public function validate($number, $countryCode = null)
    {
        $number = Regex::replace('/[^A-Z0-9]/im', '', strtoupper($number))->result();

        if (is_null($countryCode) &&
            Regex::match('/^[a-zA-Z]{2}/', $number)->hasMatch()) {
            $countryCode = substr($number, 0, 2);
        }

        if (is_null($countryCode)) {
        return false;
        }

        if (! $this->hasCountry($countryCode)) {
        return false;
        }

        $response = $this->request(
            '?' . http_build_query([
                'tk' => $this->config['api_key'],
                'op' => 'tc',
                'ca' => $countryCode,
                'tn' => strtoupper($number),
            ]),
            'get'
        );

        if (($response['result'] ?? null) == 'valid') {
            return [
                'valid' => true,
                'country_code' => strtoupper($countryCode),
                'number' => strtoupper($number),
                'type' => TaxType::TIN,
            ];
        }

        return false;

    }

    public function hasCountry($countryCode)
    {
        return in_array(
            strtolower($countryCode),
            $this->config['countrycodes']
        );
    }
}
