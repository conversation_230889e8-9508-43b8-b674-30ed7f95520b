<?php

namespace App\Services\APIS;

use Exception;
use Google\Auth\Credentials\ServiceAccountCredentials;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\Pool;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Log;
use Storage;
use Str;

class APIService
{
    protected $configPath;
    protected $config;

    protected $authentication;

    protected $storagePathPrefix;
    protected $storageExpireIn;

    public function __construct()
    {
        $this->setConfig();

        $this->storagePathPrefix = Arr::get($this->config, 'storage.path', $this->configPath . '_');
        $this->storageExpireIn = Arr::get($this->config, 'storage.expire_in', (60 * 60) - 300);

        if (! $this->authentication) {
            $this->authenticate();
        }
    }

    public function baseUrl($withPath = true)
    {
        $host = Arr::get($this->config, 'host');
        $protocol = Arr::get($this->config, 'protocol', 'https');
        $path = Arr::get($this->config, 'path', null);
        $port = ! is_null(Arr::get($this->config, 'port', null))
            ? ':' . Arr::get($this->config, 'port')
            : ''
        ;

        return $protocol . '://' . $host . $port . '/' . ($withPath && ! is_null($path)
            ? $path . '/'
            : ''
        );
    }

    public function getUrl($endpoint)
    {
        return implode('', $this->getRequestParameters($endpoint));
    }

    public function batchRequest(
        array $requests = [],
        $method = 'get',
        $data = null,
        $headers = []
    ) {

        $responses = Http::pool(function (Pool $pool) use (
            $requests,
            $method,
            $data,
            $headers
        ) {

            $pendingRequests = [];

            foreach ($requests as $name => $req) {

                if (is_string($req)) {
                    $req = ['endpoint' => $req];
                }

                $pendingRequests[$name] = $this->buildRequest(
                    $pool->as($name),
                    $req['endpoint'],
                    $req['headers'] ?? $headers
                )->{strtolower($req['method'] ?? $method)}(
                    '{+url}{+endpoint}',
                    ($req['data'] ?? $data)
                );
            }

            return $pendingRequests;
        });

        return Arr::map($responses, function ($response) {
            return $this->getRequestResponse($response);
        });
    }

    public function request(
        $endpoint,
        $method = 'get',
        $data = [],
        $headers = null
    ) {

        try {

            return $this->getRequestResponse(
                $this->buildRequest(
                    Http::withOptions([]),
                    $endpoint,
                    $headers
                )->send(
                    $method,
                    '{+url}{+endpoint}',
                    $data
                )
            );
        } catch (Exception $e) {
            \Log::error($e);

            return false;
        }
    }

    public function authenticate()
    {
        if (! $this->config['authentication']) {
        return true;
        }

       // if ($this->config['authentication']['type'] == 'parameter') return true;

        if ($this->authentication) {
        return true;
        }

        if (Cache::has($key = $this->storagePathPrefix . 'authentication')) {
            $this->authentication = Cache::get($key);

            return true;
        }

        $methodName = 'authenticate' . ucfirst(Str::camel(Arr::get($this->config, 'authentication.type')));

        if (! method_exists($this, $methodName)) {
        return false;
        }

        if (($response = $this->{$methodName}()) === false) {
        return false;
        }

        $this->authentication = Cache::remember(
            $this->storagePathPrefix . 'authentication',
            ($response['expires_in'] ?? $this->storageExpireIn),
            function () use ($response) {
                return $response['access_token'];
            }
        );

        return true;
    }

    public function authenticateGoogleAuth()
    {
        try {

            $credentials = new ServiceAccountCredentials(
                Arr::get($this->config, 'authentication.scopes', []),
                json_decode(
                    Storage::get(Arr::get($this->config, 'authentication.credentials_file')),
                    true
            ));

            $accessToken = $credentials->fetchAuthToken();

            if (! is_array($accessToken) ||
                ! isset($accessToken['access_token'])) {
                return false;
            }

            $accessToken['expires_in'] -= 300;

            return $accessToken;

        } catch (\Exception $e) {
            return false;
        }
    }

    public function authenticateOauth2()
    {
        $response = Http::asForm()
            ->withBasicAuth(
                Arr::get($this->config, 'authentication.key'),
                Arr::get($this->config, 'authentication.secret')
            )->post($this->baseUrl(false) . Arr::get($this->config, 'authentication.url_token'), [
                'grant_type' => Arr::get($this->config, 'authentication.grant_type'),
            ])
        ;

        if (! $response->successful() ||
            ! isset($response->json()['access_token'])) {
            return false;
        }

        return $response->json();
    }

    private function buildRequest(
        $pendingRequest,
        $endpoint,
        $headers = []
    ): PendingRequest {

        $pendingRequest = $pendingRequest
            ->connectTimeout(Arr::get($this->config, 'timeout.connect', 2))
            ->timeout(Arr::get($this->config, 'timeout.response', 5))
            ->retry(
                Arr::get($this->config, 'retries.attempts', 1),
                Arr::get($this->config, 'retries.interval', 250)
            )
            ->withOptions($this->getRequestOptions())
            ->withHeaders($this->getRequestHeaders($headers))
            ->withUrlParameters($this->getRequestParameters($endpoint))
        ;

        Log::info($this->getRequestParameters($endpoint));

        if ($this->authentication) {
            $pendingRequest->withToken($this->authentication);
        }

        return $pendingRequest;
    }

    private function getRequestResponse($response)
    {
        if ($response instanceof RequestException) {
        return false;
        }

        // if ($response->failed()) {
        //     $errorData = $response->json();
        //     dd($errorData);
        // }

        if (! $response->successful()) {
        return false;
        }

        return strpos($response->header('Content-Type'), 'application/json') !== false
            ? $response->json()
            : $response->body()
        ;
    }

    private function getRequestOptions()
    {

        $options = [
            'http_errors' => false,
        ];

        if (Arr::get($this->config, 'authentication.type', null) == 'ntlm') {
            $options['auth'] = [
                Arr::get($this->config, 'authentication.user', ''),
                Arr::get($this->config, 'authentication.pass', ''),
                'ntlm',
            ];
        }

        return $options;
    }

    private function getRequestParameters($endpoint)
    {
        return [
            'url' => str_starts_with($endpoint, 'http')
                ? ''
                : $this->baseUrl(),
            'endpoint' => $endpoint,
        ];
    }

    private function getRequestHeaders($headers)
    {
        $headers ??= [];

        $headers = array_merge(Arr::get($this->config, 'headers', []));

        if (Arr::get($this->config, 'accept', 'json') == 'json') {
            $headers['Accept'] = 'application/json';
        }

        if (Arr::get($this->config, 'content', 'json') == 'json') {
            $headers['Content-Type'] = 'application/json';
        }

        return $headers;
    }

    private function setConfig()
    {
        $this->configPath = 'apis.' . strtolower(preg_replace(
            '/APIService$/',
            '',
            basename(str_replace('\\', '/', get_class($this)))
        ));

        $this->config = $this->processConfigPlaceholders(config($this->configPath));
    }

    private function processConfigPlaceholders(array $config): array
    {
        foreach ($config as $key => $value) {
            if (is_array($value)) {
                $config[$key] = $this->processConfigPlaceholders($value);
            } elseif (is_string($value)) {
                $config[$key] = $this->replacePlaceholders($value);
            }
        }

        return $config;
    }

    private function replacePlaceholders($string): string
    {
        return preg_replace_callback('/\{\{\s*config\.([^\}]+)\s*\}\}/', function ($matches) {

            $value = config(trim($matches[1]));

            if ($value !== null) {
            return $value;
            }

            return $matches[0];

        }, $string);
    }
}
