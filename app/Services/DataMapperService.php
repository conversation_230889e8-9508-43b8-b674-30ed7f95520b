<?php

namespace App\Services;

use App\Contracts\TransformerContract;
use Brick\PhoneNumber\PhoneNumber;
use Brick\PhoneNumber\PhoneNumberFormat;
use Exception;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\MessageBag;
use stdClass;
use Validator;
use VIISON\AddressSplitter\AddressSplitter;
use VIISON\AddressSplitter\Exceptions\SplittingException;

class DataMapperService
{
    public MessageBag $errors;

    protected array $validatedData = [];

    public function __construct(
        public array $mappings,
        public array|Arrayable|Collection|Model|stdClass $inputData,
        public string $keyPrefix = ''
    ) {
        if ($this->inputData instanceof Arrayable ||
            $this->inputData instanceof Model ||
            $this->inputData instanceof Collection) {
            $this->inputData = $this->inputData->toArray();
        }

        if ($this->inputData instanceof stdClass) {
            $this->inputData = (array) $this->inputData;
        }

        $this->inputData = Arr::dot($this->inputData);

        $this->errors = new MessageBag();

        $this->mapAndValidate();
    }

    public function mapAndValidate()
    {
        $this->validatedData = [];

        // preform prepares
        $rulesData = [];
        $inputData = [];
        foreach ($this->mappings as $attribute => $mapping) {

            $key = $mapping['key'] ?? $mapping['key_alternative'] ?? null;

            if (is_null($key)) {
            continue;
            }

            $value = data_get(
                $this->inputData,
                $this->keyPrefix . $key,
                $mapping['default'] ?? null
            );

            if (isset($mapping['before'])) {

                if (! is_array($mapping['before'])) {
                    $mapping['before'] = [$mapping['before']];
                }

                foreach ($mapping['before'] as $before) {

                    if (! is_string($before) ||
                        ! class_exists($before)) {
                        continue;
                    }

                    $before = new $before();

                    if (($before instanceof TransformerContract) === false) {
                        continue;
                    }

                    if ($before->canTransform($value)) {
                        $value = $before->transform($value);
                    }
                }
            }

            $value ??= $mapping['default'] ?? null;

            if (($mapping['value_if_invalid'] ?? null) &&
                ! $this->isValidAttribute($attribute, $value, $mapping['rules'])) {
                $value = $mapping['value_if_invalid'];
            }

            $inputData[$attribute] = $value;
            $rulesData[$attribute] = $mapping['rules'];
        }

        // do validation
        $this->validatedData = $this->validateAttributes(
            $inputData,
            $rulesData
        );

        // foreach ($this->mappings as $attribute => $mapping) {
        //     $this->validatedData[$attribute] = $validated[$mapping['key']] ?? null;
        // }

        // foreach ($this->mappings as $attribute => $mapping) {

        //     $value = $this->inputData[$mapping['key']] ?? null;

        //     if (isset($mapping['transform']) &&
        //         is_callable($mapping['transform'])) {
        //         $value = call_user_func($mapping['transform'], $value);
        //     }

        //     $this->validatedData[$attribute] = $this->validateAttribute(
        //         $attribute,
        //         $value,
        //         $mapping['rules']
        //     );
        // }

    }

    public function validated()
    {
        return $this->validatedData;
    }

    public function hasErrors()
    {
        return $this->errors->count() > 0;
    }

    protected function validateAttributes(array $data, array $rules)
    {
        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            $this->errors = $validator->errors();

            return [];
        }

        return $validator->validated();
    }

    protected function isValidAttribute($attribute, $value, $rules)
    {
        $validator = Validator::make([
            $attribute => $value,
        ], [
            $attribute => $rules,
        ]);

        return $validator->fails()
            ? false
            : true
        ;
    }

    // DEPRECATED
    public static function transformToUrl($url, $data = null): string|null
    {
        $parsedUrl = parse_url($url);

        $parsedUrl['scheme'] ??= 'https';

        if (! isset($parsedUrl['host'])) {
            return null;
        }

        return $parsedUrl['scheme'] . '://' . $parsedUrl['host'];
    }

    public static function transformToPhoneNumber($phoneNumber, $data = null): string|null
    {
        if (is_null($phoneNumber) ||
            is_null($data) ||
            ! isset($data['input']) ||
            ! isset($data['data'][$data['input']])) {
            return null;
        }

        try {
            $number = PhoneNumber::parse($phoneNumber, $data['data'][$data['input']]);
            if ($number->isValidNumber()) {
                return $number->format(PhoneNumberFormat::E164);
            }
        } catch (Exception $e) {
        }

        return null;
    }

    public static function transformToLocaleId($countryCode, $data = null): string|null
    {
        $countryCode = strtolower($countryCode);

        $countryCode = in_array($countryCode, ['gb', 'en'])
            ? 'en'
            : ($countryCode == 'be'
                ? 'nl'
                : $countryCode)
        ;

        if (! in_array($countryCode, ['us', 'nl', 'de', 'en'])) {
            return config('locales.en.id');
        }

        return config('locales.' . $countryCode . '.id');
    }

    public static function transformToStreet($addressLine, $data = null): string|null
    {

        try {
            $segments = AddressSplitter::splitAddress($addressLine);

            return $segments['streetName'];
        } catch (SplittingException $e) {
            if ($e->getCode() == 1) {
                return $addressLine;
            }
        } catch (Exception $e) {
            dd($e);
        }

        return null;
    }

    public static function transformToHousenumber($addressLine, $data = null): string|null
    {
        try {
            $segments = AddressSplitter::splitAddress($addressLine);

            return $segments['houseNumberParts']['base'] ?? null;
        } catch (Exception $e) {
            return null;
        }
    }

    public static function transformToAddition($addressLine, $data = null): string|null
    {
        try {
            $segments = AddressSplitter::splitAddress($addressLine);

            return trim(implode(' ', [
                $segments['houseNumberParts']['extension'] ?? '',
                $segments['additionToAddress2'] ?? '',
            ]));

        } catch (Exception $e) {
            return null;
        }
    }
}
